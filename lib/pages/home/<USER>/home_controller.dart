import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../types/health_record.dart';
import '../../../repository/health_record_repository.dart';
import '../../../health_service/service_manager.dart';

part 'home_controller.g.dart';

/// 首页控制器
@riverpod
class HomeController extends _$HomeController {
  @override
  Future<Map<HealthRecordTypeEnum, TodayHealthOverview>> build() async {
    return await _loadTodayHealthOverview();
  }

  /// 加载今日健康概览数据
  Future<Map<HealthRecordTypeEnum, TodayHealthOverview>> _loadTodayHealthOverview() async {
    final healthRepo = ref.read(healthRecordRepositoryProvider);

    // 获取今天的所有健康记录
    final todayRecords = await healthRepo.getTodayHealthRecords();

    // 使用 HealthServiceManager 转换为 TodayHealthOverview
    return HealthServiceManager.convertToTodayOverview(todayRecords);
  }

  /// 刷新数据
  void refresh() {
    ref.invalidateSelf();
  }
}
