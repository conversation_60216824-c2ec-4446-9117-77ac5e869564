import 'package:drift/drift.dart';
import 'package:health_diary/types/health_types.dart';

class HealthRecords extends Table {
  IntColumn get id => integer().autoIncrement()();

  TextColumn get type =>
      text().map(EnumNameConverter(HealthRecordTypeEnum.values))(); // 记录类型（枚举值）

  TextColumn get data => text().map(HealthRecordData.converter)(); // JSON数据
  DateTimeColumn get createdAt => dateTime()(); // 创建时间
}
