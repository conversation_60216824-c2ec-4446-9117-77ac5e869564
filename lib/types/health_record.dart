import 'package:flutter/material.dart';
import 'package:health_diary/types/health_types.dart';

/// 健康记录条目
class HealthRecordEntry {
  final String time;
  final String type;
  final String note;
  final HealthRecordTypeEnum recordType;
  final List<HealthRecordValue> values;
  final int color;

  const HealthRecordEntry({
    required this.time,
    required this.type,
    required this.note,
    required this.recordType,
    required this.values,
    required this.color,
  });
}

/// 健康记录数值
class HealthRecordValue {
  final String label;
  final String value;
  final String unit;

  const HealthRecordValue({
    required this.label,
    required this.value,
    required this.unit,
  });
}

class TodayHealthOverview {
  final String value;
  final String unit;
  final Widget icon;

  const TodayHealthOverview({
    required this.value,
    required this.unit,
    required this.icon,
  });
}
