import 'package:flutter/material.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_record.dart';
import 'package:health_diary/types/health_types.dart';

abstract class HealthService {
  HealthRecordEntry parseRecordEntry(HealthRecord record);

  bool canScan();

  /// 计算平均值
  TodayHealthOverview calculateAverage(List<HealthRecord> records);

  TodayHealthOverview emptyOverview();

  /// 构建输入表单
  Widget buildInputForm({required VoidCallback onSave});

  /// 获取健康状态描述
  // String getHealthStatusDescription(HealthRecordAvg average);
}
