import 'package:easy_localization/easy_localization.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_record.dart';
import 'package:health_diary/types/health_types.dart';

import 'health_service.dart';

class SugarService extends HealthService {
  @override
  HealthRecordEntry parseRecordEntry(HealthRecord record) {
    final timeFormatter = DateFormat('MM-dd HH:mm');
    final timeString = timeFormatter.format(record.createdAt);
    final sugar = record.data as BloodSugarData;

    return HealthRecordEntry(
      time: timeString,
      type: '血糖',
      note: sugar.note ?? '',
      recordType: HealthRecordTypeEnum.bloodSugar,
      values: [
        HealthRecordValue(
          label: '血糖',
          value: sugar.value.toStringAsFixed(1),
          unit: 'mmol/L',
        ),
      ],
      color: 0xFFE4F4FF, // 蓝色
    );
  }

  @override
  bool canScan() => false;

  @override
  TodayHealthOverview calculateAverage(List<HealthRecord> records) {
    if (records.isEmpty) {
      return emptyOverview();
    }

    double totalValue = 0;
    for (final record in records) {
      final sugar = record.data as BloodSugarData;
      totalValue += sugar.value;
    }

    return TodayHealthOverview(
      value: "${totalValue / records.length}",
      unit: 'mmol',
      icon: FontAwesomeIcons.droplet,
    );
  }

  @override
  TodayHealthOverview emptyOverview() {
    return const TodayHealthOverview(
      value: "--",
      unit: 'mmol',
      icon: FontAwesomeIcons.droplet,
    );
  }
}
