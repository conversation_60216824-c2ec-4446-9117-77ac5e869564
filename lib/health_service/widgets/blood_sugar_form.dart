import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:health_diary/health_service/controller/data_controller.dart';
import 'package:health_diary/types/health_types.dart';

class BloodSugarForm extends ConsumerStatefulWidget {
  final VoidCallback onSave;

  const BloodSugarForm({super.key, required this.onSave});

  @override
  ConsumerState<BloodSugarForm> createState() => _BloodSugarFormState();
}

class _BloodSugarFormState extends ConsumerState<BloodSugarForm> {
  final _formKey = GlobalKey<FormState>();
  final _dateTimeController = TextEditingController();
  final _valueController = TextEditingController();
  final _noteController = TextEditingController();
  final _valueFocusNode = FocusNode();
  final _noteFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 初始化将在build方法中处理
  }

  @override
  void dispose() {
    _dateTimeController.dispose();
    _valueController.dispose();
    _noteController.dispose();
    _valueFocusNode.dispose();
    _noteFocusNode.dispose();
    super.dispose();
  }

  KeyboardActionsConfig _buildConfig(BuildContext context) {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.ALL,
      keyboardBarColor: Colors.grey[200],
      nextFocus: true,
      actions: [
        KeyboardActionsItem(
          focusNode: _valueFocusNode,
        ),
        KeyboardActionsItem(
          focusNode: _noteFocusNode,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final data = ref.watch(dataControllerProvider);

    // 初始化数据
    if (data is BloodSugarData) {
      if (data.value != 0.0) _valueController.text = data.value.toString();
      _noteController.text = data.note ?? '';
    }

    _dateTimeController.text = DateFormat('yyyy年MM月dd日 HH:mm').format(DateTime.now());

    return KeyboardActions(
      config: _buildConfig(context),
      disableScroll: true,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          // color: Theme.of(context).colorScheme.background,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Form(
          key: _formKey,
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 日期时间输入
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('time'.tr(), style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _dateTimeController,
                  decoration: InputDecoration(
                    hintText: 'select_date_time'.tr(),
                    hintStyle: Theme.of(context).textTheme.labelMedium?.copyWith(fontWeight: FontWeight.w500),
                    suffixIcon: Icon(Icons.access_time),
                    enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Theme.of(context).dividerColor)),
                  ),
                  readOnly: true,
                  onTap: _selectDateTime,
                ),
              ],
            ),

            const SizedBox(height: 20),

            // 血糖值输入
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('blood_sugar_value'.tr(), style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _valueController,
                  focusNode: _valueFocusNode,
                  decoration: InputDecoration(
                    hintText: 'enter_value'.tr(),
                    hintStyle: Theme.of(context).textTheme.labelMedium?.copyWith(fontWeight: FontWeight.w500),
                    suffixText: 'mmol_l'.tr(),
                    enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Theme.of(context).dividerColor)),
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  textInputAction: TextInputAction.next,
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  onChanged: (value) {
                    final doubleValue = double.tryParse(value);
                    if (doubleValue != null) {
                      ref.read(dataControllerProvider.notifier).updateBloodSugar(value: doubleValue);
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'required_field'.tr();
                    }
                    final number = double.tryParse(value);
                    if (number == null || number < 1.0 || number > 30.0) {
                      return 'invalid_blood_sugar'.tr();
                    }
                    return null;
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 备注输入
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('note_optional'.tr(), style: Theme.of(context).textTheme.bodyMedium),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _noteController,
                  focusNode: _noteFocusNode,
                  decoration: InputDecoration(
                    hintText: 'add_note_hint'.tr(),
                    hintStyle: Theme.of(context).textTheme.labelMedium?.copyWith(fontWeight: FontWeight.w500),
                    enabledBorder: UnderlineInputBorder(borderSide: BorderSide(color: Theme.of(context).dividerColor)),
                  ),
                  textInputAction: TextInputAction.done,
                  onChanged: (value) {
                    ref.read(dataControllerProvider.notifier).updateBloodSugar(note: value.isNotEmpty ? value : null);
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // 提交按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _submitForm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('save_record'.tr(), style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              ),
            ),


          ],
        ),
      ),
    ));
  }

  Future<void> _selectDateTime() async {
    final currentTime = DateTime.now();

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: currentTime,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (pickedDate != null && mounted) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(currentTime),
      );

      if (pickedTime != null) {
        final newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );
        _dateTimeController.text = DateFormat('yyyy年MM月dd日 HH:mm').format(newDateTime);
      }
    }
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      widget.onSave();
    }
  }
}
