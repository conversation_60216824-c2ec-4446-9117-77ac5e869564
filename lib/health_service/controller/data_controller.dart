import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'data_controller.g.dart';

@riverpod
class DataController extends _$DataController {
  final scanParser = {
    HealthRecordTypeEnum.bloodPressure: true,
    HealthRecordTypeEnum.bloodSugar: false,
  };

  @override
  HealthRecordData? build() {
    return null;
  }

  void setBloodSugar({
    required double value,
    String? note,
  }) {
    state = HealthRecordData.bloodSugar(
      value: value,
      note: note,
    );
  }

  void setBloodPressure({
    required int systolic,
    required int diastolic,
    int? pulse,
    String? note,
  }) {
    state = HealthRecordData.bloodPressure(
      systolic: systolic,
      diastolic: diastolic,
      pulse: pulse,
      note: note,
    );
  }

  void updateBloodSugar({
    double? value,
    String? note,
  }) {
    final currentData = state;
    if (currentData is BloodSugarData) {
      state = HealthRecordData.bloodSugar(
        value: value ?? currentData.value,
        note: note ?? currentData.note,
      );
    } else {
      state = HealthRecordData.bloodSugar(
        value: value ?? 0.0,
        note: note,
      );
    }
  }

  void updateBloodPressure({
    int? systolic,
    int? diastolic,
    int? pulse,
    String? note,
  }) {
    final currentData = state;
    if (currentData is BloodPressureData) {
      state = HealthRecordData.bloodPressure(
        systolic: systolic ?? currentData.systolic,
        diastolic: diastolic ?? currentData.diastolic,
        pulse: pulse ?? currentData.pulse,
        note: note ?? currentData.note,
      );
    } else {
      state = HealthRecordData.bloodPressure(
        systolic: systolic ?? 0,
        diastolic: diastolic ?? 0,
        pulse: pulse,
        note: note,
      );
    }
  }

  bool parseBloodPressureScanData(List<int> data) {
    if (data.length < 2) {
      return false;
    }

    List<int> remainingData = List.from(data);
    int? systolic;
    int? diastolic;
    int? pulse;

    // 匹配高压 (收缩压，正常范围约90-200)
    for (int i = 0; i < remainingData.length; i++) {
      int value = remainingData[i];
      if (value >= 50 && value <= 200) {
        systolic = value;
        remainingData.removeAt(i);
        break;
      }
    }

    if (systolic == null) {
      return false;
    }

    // 匹配低压 (舒张压，正常范围约60-120)
    for (int i = 0; i < remainingData.length; i++) {
      int value = remainingData[i];
      if (value >= 30 && value <= 120 && value < systolic) {
        diastolic = value;
        remainingData.removeAt(i);
        break;
      }
    }

    if (diastolic == null) {
      return false;
    }

    // 匹配脉搏 (可选，正常范围约50-150)
    if (remainingData.isNotEmpty) {
      for (int i = 0; i < remainingData.length; i++) {
        int value = remainingData[i];
        if (value >= 40 && value <= 220) {
          pulse = value;
          break;
        }
      }
    }

    state = HealthRecordData.bloodPressure(
      systolic: systolic,
      diastolic: diastolic,
      pulse: pulse,
    );

    return true;
  }

  bool parseScanData(List<int> data, HealthRecordTypeEnum recordType) {
    switch (recordType) {
      case HealthRecordTypeEnum.bloodPressure:
        return parseBloodPressureScanData(data);
      case HealthRecordTypeEnum.bloodSugar:
        return false;
    }
  }

  bool canScan(HealthRecordTypeEnum recordType) {
    return scanParser[recordType] ?? false;
  }
}
