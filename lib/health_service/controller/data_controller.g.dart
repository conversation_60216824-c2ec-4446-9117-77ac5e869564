// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dataControllerHash() => r'e3ae348bb55fc76c69278d6cab6c07b3c72586fe';

/// See also [DataController].
@ProviderFor(DataController)
final dataControllerProvider =
    AutoDisposeNotifierProvider<DataController, HealthRecordData?>.internal(
      DataController.new,
      name: r'dataControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$dataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DataController = AutoDisposeNotifier<HealthRecordData?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
