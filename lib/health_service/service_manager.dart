import 'package:flutter/cupertino.dart';
import 'package:health_diary/health_service/sugar_service.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_types.dart';

import '../types/health_record.dart';
import 'health_service.dart';
import 'pressure_service.dart';

class HealthServiceManager {
  static final Map<HealthRecordTypeEnum, HealthService> _map = {
    HealthRecordTypeEnum.bloodPressure: PressureService(),
    HealthRecordTypeEnum.bloodSugar: SugarService(),
  };

  static HealthService getService(HealthRecordTypeEnum type) {
    return _map[type]!;
  }

  static List<HealthRecordEntry> convertToHealthRecordEntry(
    List<HealthRecord> records,
  ) {
    final entries = <HealthRecordEntry>[];
    for (final record in records) {
      final ret = _map[record.type]?.parseRecordEntry(record);
      if (ret == null) {
        continue;
      }

      entries.add(ret);
    }

    return entries;
  }

  /// 转换为今日健康概览
  static Map<HealthRecordTypeEnum, TodayHealthOverview?> convertToTodayOverview(
    List<HealthRecord> records,
  ) {
    final averages = emptyTodayOverview();
    debugPrint('records: $records, averages: $averages');
    if (records.isEmpty) {
      return averages;
    }

    // 按类型分组记录
    final recordsByType = <HealthRecordTypeEnum, List<HealthRecord>>{};
    for (final record in records) {
      recordsByType.putIfAbsent(record.type, () => []).add(record);
    }

    debugPrint('recordsByType: $recordsByType');

    for (final entry in recordsByType.entries) {
      final service = _map[entry.key];
      if (service != null) {
        try {
          averages[entry.key] = service.calculateAverage(entry.value);
        } catch (e) {
          // 如果计算平均值失败，跳过该类型
          continue;
        }
      }
    }

    return averages;
  }

  static Map<HealthRecordTypeEnum, TodayHealthOverview?> emptyTodayOverview() {
    return Map<HealthRecordTypeEnum, TodayHealthOverview?>
        .fromIterable(HealthRecordTypeEnum.values, value: (k) => null);
  }
}
