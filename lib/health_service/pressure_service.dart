import 'package:easy_localization/easy_localization.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_record.dart';
import 'package:health_diary/types/health_types.dart';

import 'health_service.dart';

class PressureService extends HealthService {
  @override
  bool canScan() => true;

  @override
  HealthRecordEntry parseRecordEntry(HealthRecord record) {
    final timeFormatter = DateFormat('MM-dd HH:mm');
    final timeString = timeFormatter.format(record.createdAt);
    final pressure = record.data as BloodPressureData;

    final values = <HealthRecordValue>[
      HealthRecordValue(
        label: '高压',
        value: pressure.systolic.toString(),
        unit: 'mmHg',
      ),
      HealthRecordValue(
        label: '低压',
        value: pressure.diastolic.toString(),
        unit: 'mmHg',
      ),
    ];

    if (pressure.pulse != null) {
      values.add(HealthRecordValue(
        label: '脉搏',
        value: pressure.pulse.toString(),
        unit: 'bpm',
      ));
    }

    return HealthRecordEntry(
      time: timeString,
      type: '血压',
      note: pressure.note ?? "",
      recordType: HealthRecordTypeEnum.bloodPressure,
      values: values,
      color: 0xFFFFEBF0, // 绿色
    );
  }

  @override
  TodayHealthOverview calculateAverage(List<HealthRecord> records) {
    if (records.isEmpty) {
      return emptyOverview();
    }

    double totalSystolic = 0;
    double totalDiastolic = 0;
    double totalPulse = 0;
    int pulseCount = 0;

    for (final record in records) {
      final pressure = record.data as BloodPressureData;
      totalSystolic += pressure.systolic;
      totalDiastolic += pressure.diastolic;
      if (pressure.pulse != null) {
        totalPulse += pressure.pulse!;
        pulseCount++;
      }
    }

    return TodayHealthOverview(
        value:"${(totalSystolic / records.length).toInt()}/${(totalDiastolic / records.length).toInt()}",
        unit: 'mmHg',
        icon: null
    );
  }

  @override
  TodayHealthOverview emptyOverview() {
    return const TodayHealthOverview(
      value: "--",
      unit: "mmHg",
      icon: null,
    );
  }
}
